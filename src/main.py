#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
网页爬虫程序 - 使用 metadata_parser 和 Selenium 混合模式获取网页元信息
支持绕过强力反爬虫保护
"""

import metadata_parser
import time
from typing import Dict, List, Optional
import warnings
import urllib3
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, WebDriverException
from webdriver_manager.chrome import ChromeDriverManager
from bs4 import BeautifulSoup

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)


class WebScraper:
    """混合模式网页爬虫类：metadata_parser + Selenium"""
    
    def __init__(self, timeout: int = 10, delay: float = 1.0, use_selenium_by_default: bool = False):
        """
        初始化爬虫
        
        Args:
            timeout: 请求超时时间（秒）
            delay: 请求间隔时间（秒）
            use_selenium_by_default: 是否默认使用 Selenium
        """
        self.timeout = timeout
        self.delay = delay
        self.use_selenium_by_default = use_selenium_by_default
        self.driver = None
        
        # 已知需要 Selenium 的网站域名
        self.selenium_required_domains = {
            'bitget.com',
            'binance.com',
            'coinbase.com',
            'okx.com',
            'bybit.com'
        }
    
    def _init_selenium(self):
        """初始化 Selenium WebDriver"""
        if self.driver is not None:
            return True
            
        try:
            print("🔧 初始化 Selenium WebDriver...")
            
            # Chrome 选项配置
            chrome_options = Options()
            chrome_options.add_argument('--headless')  # 无头模式
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-gpu')
            chrome_options.add_argument('--window-size=1920,1080')
            chrome_options.add_argument('--disable-blink-features=AutomationControlled')
            chrome_options.add_argument('--disable-web-security')
            chrome_options.add_argument('--disable-features=VizDisplayCompositor')
            chrome_options.add_argument('--disable-extensions')
            chrome_options.add_argument('--disable-plugins')
            chrome_options.add_argument('--disable-images')  # 不加载图片，提升速度
            chrome_options.add_argument('--disable-javascript')  # 对于元数据抓取，通常不需要JS
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)
            
            # 禁用不必要的功能以提升速度
            prefs = {
                "profile.managed_default_content_settings.images": 2,
                "profile.default_content_setting_values.notifications": 2,
                "profile.default_content_settings.popups": 0,
                "profile.managed_default_content_settings.media_stream": 2,
            }
            chrome_options.add_experimental_option("prefs", prefs)
            
            # 设置用户代理
            chrome_options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36')
            
            # 自动下载并设置 ChromeDriver
            service = Service(ChromeDriverManager().install())
            service.start()  # 预启动服务
            
            # 创建 WebDriver
            self.driver = webdriver.Chrome(service=service, options=chrome_options)
            
            # 设置更短的超时时间
            self.driver.set_page_load_timeout(self.timeout)
            self.driver.implicitly_wait(5)
            
            # 移除自动化检测特征
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            
            print("✅ Selenium WebDriver 初始化成功")
            return True
            
        except Exception as e:
            print(f"❌ Selenium 初始化失败: {e}")
            return False
    
    def _should_use_selenium(self, url: str) -> bool:
        """判断是否应该使用 Selenium"""
        if self.use_selenium_by_default:
            return True
            
        # 检查域名是否在需要 Selenium 的列表中
        from urllib.parse import urlparse
        domain = urlparse(url).netloc.lower()
        
        for selenium_domain in self.selenium_required_domains:
            if selenium_domain in domain:
                return True
                
        return False
    
    def get_page_meta_info_selenium(self, url: str) -> Dict[str, Optional[str]]:
        """
        使用 Selenium 获取网页元信息
        
        Args:
            url: 网页URL
            
        Returns:
            包含 title、description、keywords、canonical 的字典
        """
        result = {
            'url': url,
            'title': None,
            'description': None,
            'keywords': None,
            'canonical': None,
            'error': None,
            'method': 'selenium'
        }
        
        if not self._init_selenium():
            result['error'] = "Selenium 初始化失败"
            return result
        
        try:
            print(f"🔍 正在爬取 (Selenium): {url}")
            
            # 访问页面
            self.driver.get(url)
            
            # 等待页面加载
            WebDriverWait(self.driver, self.timeout).until(
                lambda d: d.execute_script("return document.readyState") == "complete"
            )
            
            # 额外等待，确保动态内容加载
            time.sleep(3)
            
            # 获取页面源码
            html = self.driver.page_source
            soup = BeautifulSoup(html, 'html.parser')
            
            # 获取 title
            title_element = soup.find('title')
            if title_element:
                result['title'] = title_element.get_text().strip()
            
            # 获取 description - 支持多种格式
            desc_selectors = [
                {'property': 'og:description'},
                {'name': 'description'},
                {'name': 'twitter:description'},
                {'property': 'description'}
            ]
            
            for selector in desc_selectors:
                desc_element = soup.find('meta', attrs=selector)
                if desc_element and desc_element.get('content'):
                    result['description'] = desc_element.get('content').strip()
                    break
            
            # 获取 keywords
            keywords_selectors = [
                {'name': 'keywords'},
                {'property': 'article:tag'},
                {'name': 'news_keywords'}
            ]
            
            for selector in keywords_selectors:
                keywords_element = soup.find('meta', attrs=selector)
                if keywords_element and keywords_element.get('content'):
                    result['keywords'] = keywords_element.get('content').strip()
                    break
            
            # 获取 canonical URL
            canonical_selectors = [
                ('link', {'rel': 'canonical'}),
                ('meta', {'property': 'og:url'})
            ]
            
            for tag_name, attrs in canonical_selectors:
                canonical_element = soup.find(tag_name, attrs=attrs)
                if canonical_element:
                    if tag_name == 'link':
                        canonical_url = canonical_element.get('href')
                    else:
                        canonical_url = canonical_element.get('content')
                    
                    if canonical_url:
                        result['canonical'] = canonical_url.strip()
                        break
            
            print(f"✅ 成功获取 (Selenium): {url}")
            
        except TimeoutException:
            result['error'] = f"页面加载超时 ({self.timeout}秒)"
            print(f"⏰ 超时: {url} - {result['error']}")
            
        except WebDriverException as e:
            result['error'] = f"WebDriver 错误: {str(e)}"
            print(f"🚫 WebDriver 错误: {url} - {result['error']}")
            
        except Exception as e:
            result['error'] = f"Selenium 错误: {str(e)}"
            print(f"❌ 失败 (Selenium): {url} - {result['error']}")
        
        return result
    
    def get_page_meta_info(self, url: str) -> Dict[str, Optional[str]]:
        """
        使用 metadata_parser 获取网页元信息（快速模式）
        
        Args:
            url: 网页URL
            
        Returns:
            包含 title、description、keywords、canonical 的字典
        """
        result = {
            'url': url,
            'title': None,
            'description': None,
            'keywords': None,
            'canonical': None,
            'error': None,
            'method': 'metadata_parser'
        }
        
        try:
            print(f"⚡ 正在爬取 (快速模式): {url}")
            
            # 使用 metadata_parser 获取页面信息
            page = metadata_parser.MetadataParser(
                url=url,
                requests_timeout=self.timeout,
                require_public_netloc=True,
                allow_localhosts=True,
                force_parse=True,
                ssl_verify=False
            )
            
            # 获取各项信息
            result['title'] = page.get_metadata('title')
            result['description'] = page.get_metadata('description')
            result['keywords'] = page.get_metadata('keywords')
            
            # 获取 canonical URL
            canonical = page.get_discrete_url()
            if canonical and canonical != url:
                result['canonical'] = canonical
            
            # 清理结果
            for key in ['title', 'description', 'keywords', 'canonical']:
                if result[key]:
                    result[key] = result[key].strip()
            
            print(f"✅ 成功获取 (快速模式): {url}")
            
        except Exception as e:
            error_str = str(e).lower()
            
            if "403" in error_str or "forbidden" in error_str:
                result['error'] = "访问被拒绝 (403) - 需要 Selenium 模式"
            elif "404" in error_str:
                result['error'] = "页面未找到 (404)"
            elif "timeout" in error_str:
                result['error'] = f"请求超时 ({self.timeout}秒)"
            else:
                result['error'] = f"快速模式失败: {str(e)}"
                
            print(f"❌ 失败 (快速模式): {url} - {result['error']}")
        
        return result
    
    def get_page_meta_info_smart(self, url: str) -> Dict[str, Optional[str]]:
        """
        智能模式：自动选择最佳爬取方法
        
        Args:
            url: 网页URL
            
        Returns:
            包含 title、description、keywords、canonical 的字典
        """
        # 判断是否需要 Selenium
        if self._should_use_selenium(url):
            print(f"🧠 智能模式：检测到 {url} 需要 Selenium")
            return self.get_page_meta_info_selenium(url)
        
        # 先尝试快速模式
        print(f"🧠 智能模式：先尝试快速模式")
        result = self.get_page_meta_info(url)
        
        # 如果快速模式因为反爬虫失败，自动切换到 Selenium
        if result['error'] and '403' in result['error']:
            print(f"🧠 智能模式：快速模式被阻止，自动切换到 Selenium")
            return self.get_page_meta_info_selenium(url)
        
        return result
    
    def scrape_multiple_urls(self, urls: List[str], use_smart_mode: bool = True) -> List[Dict[str, Optional[str]]]:
        """
        爬取多个URL的元信息
        
        Args:
            urls: URL列表
            use_smart_mode: 是否使用智能模式
            
        Returns:
            包含所有网页元信息的列表
        """
        results = []
        
        for i, url in enumerate(urls):
            if i > 0:
                print(f"⏳ 等待 {self.delay} 秒...")
                time.sleep(self.delay)
            
            if use_smart_mode:
                result = self.get_page_meta_info_smart(url)
            else:
                result = self.get_page_meta_info(url)
                
            results.append(result)
        
        return results
    
    def print_results(self, results: List[Dict[str, Optional[str]]]):
        """
        格式化打印结果
        
        Args:
            results: 爬取结果列表
        """
        print("\n" + "="*80)
        print("爬取结果汇总")
        print("="*80)
        
        for i, result in enumerate(results, 1):
            print(f"\n【网站 {i}】")
            print(f"URL: {result['url']}")
            print(f"方法: {result.get('method', '未知')}")
            
            if result['error']:
                print(f"❌ 错误: {result['error']}")
                continue
            
            print(f"标题 (Title): {result['title'] or '未找到'}")
            print(f"描述 (Description): {result['description'] or '未找到'}")
            print(f"关键词 (Keywords): {result['keywords'] or '未找到'}")
            print(f"规范链接 (Canonical): {result['canonical'] or '未找到'}")
            print("-" * 60)
    
    def cleanup(self):
        """清理资源"""
        if self.driver:
            try:
                self.driver.quit()
                print("🧹 Selenium WebDriver 已关闭")
            except:
                pass
            finally:
                self.driver = None


def main():
    """主函数"""
    print("🕷️  混合模式网页元信息爬虫程序")
    print("🚀 metadata_parser + Selenium = 无敌组合")
    print("=" * 70)
    
    # 要爬取的网址列表
    urls = [
        "https://www.gate.com/zh",       # 普通网站 (使用快速模式)
        "https://www.bitget.com/zh-CN",  # 强防护网站 (自动使用 Selenium)
        "https://github.com",            # 普通网站
        "https://www.python.org",        # 普通网站
    ]
    
    # 创建爬虫实例
    scraper = WebScraper(timeout=20, delay=3.0)
    
    try:
        print(f"📋 准备爬取 {len(urls)} 个网站...")
        print("🧠 使用智能模式：自动选择最佳爬取方法")
        print()
        
        # 开始爬取
        results = scraper.scrape_multiple_urls(urls, use_smart_mode=True)
        
        # 打印结果
        scraper.print_results(results)
        
        # 统计
        success_count = sum(1 for r in results if not r['error'])
        total_count = len(results)
        selenium_count = sum(1 for r in results if r.get('method') == 'selenium')
        fast_count = sum(1 for r in results if r.get('method') == 'metadata_parser')
        
        print(f"\n📊 爬取完成: 成功 {success_count}/{total_count} 个网站")
        print(f"📈 方法统计: Selenium {selenium_count}个, 快速模式 {fast_count}个")
        
        # 显示优势
        print(f"\n🎯 混合模式的优势:")
        print("   ✅ 智能选择：自动识别需要 Selenium 的网站")
        print("   ⚡ 快速高效：普通网站使用快速模式")
        print("   🛡️  突破防护：强防护网站使用 Selenium")
        print("   🔄 自动切换：403错误时自动升级到 Selenium")
        print("   🎭 真实浏览：完全模拟真实用户行为")
        
    except KeyboardInterrupt:
        print("\n\n⏹️  用户中断了程序")
    except Exception as e:
        print(f"\n\n❌ 程序出现错误: {e}")
    finally:
        # 清理资源
        scraper.cleanup()


if __name__ == "__main__":
    main()