from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.firefox.options import Options
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
import pandas as pd
import time


url_df = pd.read_excel("url_metadata.xlsx")
print(url_df.head())

# 配置Firefox浏览器选项，模拟真实浏览器
firefox_options = Options()

# 设置User-Agent，模拟真实Firefox浏览器
firefox_options.set_preference("general.useragent.override", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:120.0) Gecko/20100101 Firefox/120.0")

# 关键优化：设置页面加载策略为none，get()直接返回不等待页面完全加载
firefox_options.set_preference("webdriver.load.strategy", "unstable")

# 禁用图片加载，提高速度
firefox_options.set_preference("permissions.default.image", 2)

# 禁用CSS加载，进一步提升速度（可选）
firefox_options.set_preference("permissions.default.stylesheet", 2)

# 禁用JavaScript（如果不需要的话）
firefox_options.set_preference("javascript.enabled", False)

# 禁用Flash
firefox_options.set_preference("dom.ipc.plugins.enabled.libflashplayer.so", False)

# 禁用通知
firefox_options.set_preference("dom.webnotifications.enabled", False)
firefox_options.set_preference("dom.push.enabled", False)

# 禁用地理位置
firefox_options.set_preference("geo.enabled", False)

# 设置语言
firefox_options.set_preference("intl.accept_languages", "zh-CN,zh,en-US,en")

# 禁用自动更新
firefox_options.set_preference("app.update.enabled", False)

# 禁用扩展和插件
firefox_options.set_preference("extensions.update.enabled", False)
firefox_options.set_preference("plugin.scan.plid.all", False)

# 设置窗口大小
firefox_options.add_argument("--width=1920")
firefox_options.add_argument("--height=1080")

# 可选：设置为无头模式（如果不需要看到浏览器界面）
# firefox_options.add_argument("--headless")

# 在循环外启动浏览器
driver = webdriver.Firefox(options=firefox_options)

# 设置页面加载超时时间
driver.set_page_load_timeout(10)

# 创建显式等待对象，最多等待10秒
wait = WebDriverWait(driver, 10)

print("Firefox浏览器已启动，开始处理URL...")

for index, row in url_df.iterrows():
    url = row["url"]
    print(f"正在处理第 {index + 1} 个URL: {url}")
    
    try:
        # 访问网页（现在会立即返回，不等待页面完全加载）
        driver.get(url)
        print("URL请求已发送，等待基本DOM加载...")
        
        # 等待页面title出现，表示基本DOM已加载
        try:
            wait.until(lambda driver: driver.title and len(driver.title) > 0)
            print("页面title已加载，开始获取metadata")
        except:
            print("等待title超时，继续尝试获取metadata")
        
        # 检查页面是否成功加载
        current_url = driver.current_url
        page_source_length = len(driver.page_source)
        print(f"当前URL: {current_url}")
        print(f"页面源码长度: {page_source_length}")
        
        if page_source_length < 100:
            print("警告: 页面内容过少，可能加载失败")
            # 再等待一下，有些页面需要额外时间
            time.sleep(2)
            page_source_length = len(driver.page_source)
            print(f"重新检查后页面源码长度: {page_source_length}")
        
        # 获取网页metadata
        print("--- 开始获取SEO相关信息 ---")
        
        # 获取页面标题
        title = driver.title
        print(f"页面标题: {title}")
        
        # 获取meta description
        try:
            # 等待meta description元素出现
            meta_desc_element = wait.until(
                EC.presence_of_element_located((By.XPATH, "//meta[@name='description']"))
            )
            meta_description = meta_desc_element.get_attribute("content")
            print(f"Meta描述: {meta_description}")
            print(f"描述长度: {len(meta_description)} 字符")
        except:
            try:
                # 尝试其他可能的description属性
                meta_desc_element = driver.find_element(By.XPATH, "//meta[@property='og:description']")
                meta_description = meta_desc_element.get_attribute("content")
                print(f"OG描述: {meta_description}")
                print(f"描述长度: {len(meta_description)} 字符")
            except:
                print("Meta描述: 未找到")
        
        # 获取meta keywords
        try:
            meta_keywords_element = driver.find_element(By.XPATH, "//meta[@name='keywords']")
            meta_keywords = meta_keywords_element.get_attribute("content")
            print(f"Meta关键词: {meta_keywords}")
        except:
            print("Meta关键词: 未找到")
        
        # 获取meta robots
        try:
            meta_robots_element = driver.find_element(By.XPATH, "//meta[@name='robots']")
            meta_robots = meta_robots_element.get_attribute("content")
            print(f"Meta Robots: {meta_robots}")
        except:
            print("Meta Robots: 未找到")
        
        # 获取viewport设置
        try:
            viewport_element = driver.find_element(By.XPATH, "//meta[@name='viewport']")
            viewport = viewport_element.get_attribute("content")
            print(f"Viewport设置: {viewport}")
        except:
            print("Viewport: 未找到")
        
        # 获取charset编码
        try:
            charset_element = driver.find_element(By.XPATH, "//meta[@charset]")
            charset = charset_element.get_attribute("charset")
            print(f"字符编码: {charset}")
        except:
            try:
                charset_element = driver.find_element(By.XPATH, "//meta[@http-equiv='Content-Type']")
                charset = charset_element.get_attribute("content")
                print(f"字符编码: {charset}")
            except:
                print("字符编码: 未找到")
        
        # 获取canonical链接
        try:
            canonical_element = driver.find_element(By.XPATH, "//link[@rel='canonical']")
            canonical = canonical_element.get_attribute("href")
            print(f"Canonical链接: {canonical}")
        except:
            print("Canonical链接: 未找到")
        
        # 获取其他重要的meta标签
        try:
            meta_author_element = driver.find_element(By.XPATH, "//meta[@name='author']")
            meta_author = meta_author_element.get_attribute("content")
            print(f"作者: {meta_author}")
        except:
            print("作者: 未找到")
        
        # === Open Graph标签 ===
        print("\n--- Open Graph信息 ---")
        og_tags = {
            "og:title": "OG标题",
            "og:description": "OG描述", 
            "og:image": "OG图片",
            "og:url": "OG URL",
            "og:type": "OG类型",
            "og:site_name": "网站名称"
        }
        
        for property_name, display_name in og_tags.items():
            try:
                og_element = driver.find_element(By.XPATH, f"//meta[@property='{property_name}']")
                og_content = og_element.get_attribute("content")
                print(f"{display_name}: {og_content}")
            except:
                print(f"{display_name}: 未找到")
        
        # === Twitter Card标签 ===
        print("\n--- Twitter Card信息 ---")
        twitter_tags = {
            "twitter:card": "卡片类型",
            "twitter:title": "Twitter标题",
            "twitter:description": "Twitter描述",
            "twitter:image": "Twitter图片",
            "twitter:site": "Twitter站点",
            "twitter:creator": "Twitter创建者"
        }
        
        for name, display_name in twitter_tags.items():
            try:
                twitter_element = driver.find_element(By.XPATH, f"//meta[@name='{name}']")
                twitter_content = twitter_element.get_attribute("content")
                print(f"{display_name}: {twitter_content}")
            except:
                print(f"{display_name}: 未找到")
        
        # === 标题层级结构分析 ===
        print("\n--- 标题层级结构 ---")
        for i in range(1, 7):
            try:
                headings = driver.find_elements(By.TAG_NAME, f"h{i}")
                print(f"H{i}标签数量: {len(headings)}")
                if headings:
                    # 显示前3个标题内容
                    for j, heading in enumerate(headings[:3]):
                        text = heading.text.strip()
                        if text:
                            print(f"  H{i}-{j+1}: {text[:50]}...")
            except:
                print(f"H{i}标签: 获取失败")
        
        # === 结构化数据 ===
        print("\n--- 结构化数据 ---")
        try:
            # JSON-LD结构化数据
            json_ld_elements = driver.find_elements(By.XPATH, "//script[@type='application/ld+json']")
            print(f"JSON-LD数据块数量: {len(json_ld_elements)}")
            for i, element in enumerate(json_ld_elements[:2]):  # 只显示前2个
                content = element.get_attribute("innerHTML")
                if content:
                    print(f"JSON-LD-{i+1}: {content[:100]}...")
        except:
            print("JSON-LD数据: 获取失败")
        
        # === 链接分析 ===
        print("\n--- 链接分析 ---")
        try:
            # 内部链接
            internal_links = driver.find_elements(By.XPATH, f"//a[starts-with(@href, '/') or contains(@href, '{driver.current_url.split('/')[2]}')]")
            print(f"内部链接数量: {len(internal_links)}")
            
            # 外部链接
            external_links = driver.find_elements(By.XPATH, "//a[starts-with(@href, 'http') and not(contains(@href, '" + driver.current_url.split('/')[2] + "'))]")
            print(f"外部链接数量: {len(external_links)}")
            
            # 所有链接
            all_links = driver.find_elements(By.TAG_NAME, "a")
            print(f"总链接数量: {len(all_links)}")
        except Exception as e:
            print(f"链接分析失败: {e}")
        
        # === 图片分析 ===
        print("\n--- 图片SEO分析 ---")
        try:
            images = driver.find_elements(By.TAG_NAME, "img")
            print(f"图片总数: {len(images)}")
            
            images_without_alt = 0
            for img in images:
                alt_text = img.get_attribute("alt")
                if not alt_text or alt_text.strip() == "":
                    images_without_alt += 1
            
            print(f"缺少alt属性的图片: {images_without_alt}")
            print(f"SEO优化率: {((len(images) - images_without_alt) / len(images) * 100):.1f}%" if len(images) > 0 else "无图片")
        except:
            print("图片分析: 获取失败")
        
        # === 页面基本信息 ===
        print("\n--- 页面基本信息 ---")
        try:
            # 页面语言
            html_element = driver.find_element(By.TAG_NAME, "html")
            lang = html_element.get_attribute("lang")
            print(f"页面语言: {lang if lang else '未设置'}")
            
            # 页面大小
            page_size = len(driver.page_source)
            print(f"页面源码大小: {page_size:,} 字符")
            
            # 文本内容长度估算
            body_text = driver.find_element(By.TAG_NAME, "body").text
            text_length = len(body_text.strip())
            print(f"页面文本长度: {text_length:,} 字符")
            
        except Exception as e:
            print(f"页面基本信息获取失败: {e}")
        
        # === 移动端优化检查 ===
        print("\n--- 移动端优化检查 ---")
        try:
            # 检查是否有移动端适配
            mobile_elements = driver.find_elements(By.XPATH, "//meta[@name='viewport']")
            if mobile_elements:
                print("✓ 已设置viewport（移动端适配）")
            else:
                print("✗ 未设置viewport（可能缺少移动端适配）")
                
            # 检查响应式设计相关CSS
            responsive_css = driver.find_elements(By.XPATH, "//link[contains(@href, 'responsive') or contains(@href, 'mobile')]")
            if responsive_css:
                print(f"✓ 发现响应式CSS文件: {len(responsive_css)}个")
            else:
                print("? 未明确发现响应式CSS文件")
                
        except:
            print("移动端优化检查: 获取失败")
        
        print("--- SEO信息获取完成 ---")
        print("=" * 50)
        
        # 如果有多个标签页，关闭当前标签页
        if len(driver.window_handles) > 1:
            driver.close()
            driver.switch_to.window(driver.window_handles[0])
        
        # 简短等待再处理下一个URL（现在不需要长时间等待了）
        time.sleep(0.5)
        
    except Exception as e:
        print(f"处理URL时出错: {e}")
        print("=" * 50)

# 所有URL处理完后关闭浏览器
driver.quit()
print("所有URL处理完成，Firefox浏览器已关闭")