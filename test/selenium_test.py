from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.firefox.options import Options
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
import pandas as pd
import time


url_df = pd.read_excel("url_metadata_binance.xlsx")
print(url_df.head())

# 初始化结果列表，用于存储每个URL的metadata
results = []

# 配置Firefox浏览器选项，模拟真实浏览器
firefox_options = Options()

# 设置User-Agent，模拟真实Firefox浏览器
firefox_options.set_preference("general.useragent.override", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:120.0) Gecko/20100101 Firefox/120.0")

# 关键优化：设置页面加载策略为none，get()直接返回不等待页面完全加载
firefox_options.set_preference("webdriver.load.strategy", "unstable")

# 禁用图片加载，提高速度
firefox_options.set_preference("permissions.default.image", 2)

# 禁用CSS加载，进一步提升速度（可选）
firefox_options.set_preference("permissions.default.stylesheet", 2)

# 禁用JavaScript（如果不需要的话）
firefox_options.set_preference("javascript.enabled", False)

# 禁用Flash
firefox_options.set_preference("dom.ipc.plugins.enabled.libflashplayer.so", False)

# 禁用通知
firefox_options.set_preference("dom.webnotifications.enabled", False)
firefox_options.set_preference("dom.push.enabled", False)

# 禁用地理位置
firefox_options.set_preference("geo.enabled", False)

# 设置语言
firefox_options.set_preference("intl.accept_languages", "zh-CN,zh,en-US,en")

# 禁用自动更新
firefox_options.set_preference("app.update.enabled", False)

# 禁用扩展和插件
firefox_options.set_preference("extensions.update.enabled", False)
firefox_options.set_preference("plugin.scan.plid.all", False)

# 设置窗口大小
firefox_options.add_argument("--width=1920")
firefox_options.add_argument("--height=1080")

# 可选：设置为无头模式（如果不需要看到浏览器界面）
# firefox_options.add_argument("--headless")

# 在循环外启动浏览器
driver = webdriver.Firefox(options=firefox_options)

# 设置页面加载超时时间
driver.set_page_load_timeout(10)

# 创建显式等待对象，最多等待10秒
wait = WebDriverWait(driver, 10)

print("Firefox浏览器已启动，开始处理URL...")

for index, row in url_df.iterrows():
    url = row["url"]
    print(f"正在处理第 {index + 1} 个URL: {url}")

    # 初始化当前URL的结果字典
    result = {"url": url}

    try:
        # 访问网页（现在会立即返回，不等待页面完全加载）
        driver.get(url)
        print("URL请求已发送，等待基本DOM加载...")

        # 等待页面title出现，表示基本DOM已加载
        try:
            wait.until(lambda driver: driver.title and len(driver.title) > 0)
            print("页面title已加载，开始获取metadata")
        except:
            print("等待title超时，继续尝试获取metadata")

        # 检查页面是否成功加载
        current_url = driver.current_url
        page_source_length = len(driver.page_source)
        print(f"当前URL: {current_url}")
        print(f"页面源码长度: {page_source_length}")

        if page_source_length < 100:
            print("警告: 页面内容过少，可能加载失败")
            # 再等待一下，有些页面需要额外时间
            time.sleep(2)
            page_source_length = len(driver.page_source)
            print(f"重新检查后页面源码长度: {page_source_length}")

        # 获取网页metadata
        print("--- 开始获取SEO相关信息 ---")

        # 获取页面标题
        try:
            title = driver.title
            result["title"] = title
            print(f"页面标题: {title}")
        except:
            result["title"] = ""
            print("页面标题: 获取失败")

        # 获取meta description
        try:
            # 等待meta description元素出现
            meta_desc_element = wait.until(
                EC.presence_of_element_located((By.XPATH, "//meta[@name='description']"))
            )
            meta_description = meta_desc_element.get_attribute("content")
            result["meta_description"] = meta_description
            print(f"Meta描述: {meta_description}")
            print(f"描述长度: {len(meta_description)} 字符")
        except:
            try:
                # 尝试其他可能的description属性
                meta_desc_element = driver.find_element(By.XPATH, "//meta[@property='og:description']")
                meta_description = meta_desc_element.get_attribute("content")
                result["meta_description"] = meta_description
                print(f"OG描述: {meta_description}")
                print(f"描述长度: {len(meta_description)} 字符")
            except:
                result["meta_description"] = ""
                print("Meta描述: 未找到")

        # 获取meta keywords
        try:
            meta_keywords_element = driver.find_element(By.XPATH, "//meta[@name='keywords']")
            meta_keywords = meta_keywords_element.get_attribute("content")
            result["meta_keywords"] = meta_keywords
            print(f"Meta关键词: {meta_keywords}")
        except:
            result["meta_keywords"] = ""
            print("Meta关键词: 未找到")

        # 获取meta robots
        try:
            meta_robots_element = driver.find_element(By.XPATH, "//meta[@name='robots']")
            meta_robots = meta_robots_element.get_attribute("content")
            result["meta_robots"] = meta_robots
            print(f"Meta Robots: {meta_robots}")
        except:
            result["meta_robots"] = ""
            print("Meta Robots: 未找到")

        # 获取viewport设置
        try:
            viewport_element = driver.find_element(By.XPATH, "//meta[@name='viewport']")
            viewport = viewport_element.get_attribute("content")
            result["viewport"] = viewport
            print(f"Viewport设置: {viewport}")
        except:
            result["viewport"] = ""
            print("Viewport: 未找到")

        # 获取charset编码
        try:
            charset_element = driver.find_element(By.XPATH, "//meta[@charset]")
            charset = charset_element.get_attribute("charset")
            result["charset"] = charset
            print(f"字符编码: {charset}")
        except:
            try:
                charset_element = driver.find_element(By.XPATH, "//meta[@http-equiv='Content-Type']")
                charset = charset_element.get_attribute("content")
                result["charset"] = charset
                print(f"字符编码: {charset}")
            except:
                result["charset"] = ""
                print("字符编码: 未找到")

        # 获取canonical链接
        try:
            canonical_element = driver.find_element(By.XPATH, "//link[@rel='canonical']")
            canonical = canonical_element.get_attribute("href")
            result["canonical"] = canonical
            print(f"Canonical链接: {canonical}")
        except:
            result["canonical"] = ""
            print("Canonical链接: 未找到")

        # 获取其他重要的meta标签
        try:
            meta_author_element = driver.find_element(By.XPATH, "//meta[@name='author']")
            meta_author = meta_author_element.get_attribute("content")
            result["meta_author"] = meta_author
            print(f"作者: {meta_author}")
        except:
            result["meta_author"] = ""
            print("作者: 未找到")
        
        # === Open Graph标签 ===
        print("\n--- Open Graph信息 ---")
        og_tags = {
            "og:title": "og_title",
            "og:description": "og_description",
            "og:image": "og_image",
            "og:url": "og_url",
            "og:type": "og_type",
            "og:site_name": "og_site_name"
        }

        for property_name, field_name in og_tags.items():
            try:
                og_element = driver.find_element(By.XPATH, f"//meta[@property='{property_name}']")
                og_content = og_element.get_attribute("content")
                result[field_name] = og_content
                print(f"{property_name}: {og_content}")
            except:
                result[field_name] = ""
                print(f"{property_name}: 未找到")

        # === Twitter Card标签 ===
        print("\n--- Twitter Card信息 ---")
        twitter_tags = {
            "twitter:card": "twitter_card",
            "twitter:title": "twitter_title",
            "twitter:description": "twitter_description",
            "twitter:image": "twitter_image",
            "twitter:site": "twitter_site",
            "twitter:creator": "twitter_creator"
        }

        for name, field_name in twitter_tags.items():
            try:
                twitter_element = driver.find_element(By.XPATH, f"//meta[@name='{name}']")
                twitter_content = twitter_element.get_attribute("content")
                result[field_name] = twitter_content
                print(f"{name}: {twitter_content}")
            except:
                result[field_name] = ""
                print(f"{name}: 未找到")
        
        # === 标题层级结构分析 ===
        print("\n--- 标题层级结构 ---")
        h1_count = h2_count = h3_count = h4_count = h5_count = h6_count = 0
        h1_text = h2_text = h3_text = ""

        for i in range(1, 7):
            try:
                headings = driver.find_elements(By.TAG_NAME, f"h{i}")
                count = len(headings)
                result[f"h{i}_count"] = count
                print(f"H{i}标签数量: {count}")

                # 获取前几个标题的文本内容
                if i <= 3 and headings:
                    texts = []
                    for j, heading in enumerate(headings[:3]):
                        text = heading.text.strip()
                        if text:
                            texts.append(text[:100])  # 限制长度
                    result[f"h{i}_text"] = " | ".join(texts)
                    print(f"  H{i}内容: {result[f'h{i}_text']}")
                else:
                    if i <= 3:
                        result[f"h{i}_text"] = ""
            except:
                result[f"h{i}_count"] = 0
                if i <= 3:
                    result[f"h{i}_text"] = ""
                print(f"H{i}标签: 获取失败")

        # === 结构化数据 ===
        print("\n--- 结构化数据 ---")
        try:
            # JSON-LD结构化数据
            json_ld_elements = driver.find_elements(By.XPATH, "//script[@type='application/ld+json']")
            result["json_ld_count"] = len(json_ld_elements)
            print(f"JSON-LD数据块数量: {len(json_ld_elements)}")

            # 获取第一个JSON-LD的内容片段
            if json_ld_elements:
                content = json_ld_elements[0].get_attribute("innerHTML")
                result["json_ld_content"] = content[:200] if content else ""  # 只保存前200字符
            else:
                result["json_ld_content"] = ""
        except:
            result["json_ld_count"] = 0
            result["json_ld_content"] = ""
            print("JSON-LD数据: 获取失败")

        # === 链接分析 ===
        print("\n--- 链接分析 ---")
        try:
            # 内部链接
            internal_links = driver.find_elements(By.XPATH, f"//a[starts-with(@href, '/') or contains(@href, '{driver.current_url.split('/')[2]}')]")
            result["internal_links_count"] = len(internal_links)
            print(f"内部链接数量: {len(internal_links)}")

            # 外部链接
            external_links = driver.find_elements(By.XPATH, "//a[starts-with(@href, 'http') and not(contains(@href, '" + driver.current_url.split('/')[2] + "'))]")
            result["external_links_count"] = len(external_links)
            print(f"外部链接数量: {len(external_links)}")

            # 所有链接
            all_links = driver.find_elements(By.TAG_NAME, "a")
            result["total_links_count"] = len(all_links)
            print(f"总链接数量: {len(all_links)}")
        except Exception as e:
            result["internal_links_count"] = 0
            result["external_links_count"] = 0
            result["total_links_count"] = 0
            print(f"链接分析失败: {e}")

        # === 图片分析 ===
        print("\n--- 图片SEO分析 ---")
        try:
            images = driver.find_elements(By.TAG_NAME, "img")
            result["images_count"] = len(images)
            print(f"图片总数: {len(images)}")

            images_without_alt = 0
            for img in images:
                alt_text = img.get_attribute("alt")
                if not alt_text or alt_text.strip() == "":
                    images_without_alt += 1

            result["images_without_alt"] = images_without_alt
            result["images_seo_rate"] = round(((len(images) - images_without_alt) / len(images) * 100), 1) if len(images) > 0 else 0
            print(f"缺少alt属性的图片: {images_without_alt}")
            print(f"SEO优化率: {result['images_seo_rate']}%")
        except:
            result["images_count"] = 0
            result["images_without_alt"] = 0
            result["images_seo_rate"] = 0
            print("图片分析: 获取失败")

        # === 页面基本信息 ===
        print("\n--- 页面基本信息 ---")
        try:
            # 页面语言
            html_element = driver.find_element(By.TAG_NAME, "html")
            lang = html_element.get_attribute("lang")
            result["page_lang"] = lang if lang else ""
            print(f"页面语言: {lang if lang else '未设置'}")

            # 页面大小
            page_size = len(driver.page_source)
            result["page_size"] = page_size
            print(f"页面源码大小: {page_size:,} 字符")

            # 文本内容长度估算
            body_text = driver.find_element(By.TAG_NAME, "body").text
            text_length = len(body_text.strip())
            result["text_length"] = text_length
            print(f"页面文本长度: {text_length:,} 字符")

        except Exception as e:
            result["page_lang"] = ""
            result["page_size"] = 0
            result["text_length"] = 0
            print(f"页面基本信息获取失败: {e}")

        # === 移动端优化检查 ===
        print("\n--- 移动端优化检查 ---")
        try:
            # 检查是否有移动端适配
            mobile_elements = driver.find_elements(By.XPATH, "//meta[@name='viewport']")
            result["has_viewport"] = len(mobile_elements) > 0
            if mobile_elements:
                print("✓ 已设置viewport（移动端适配）")
            else:
                print("✗ 未设置viewport（可能缺少移动端适配）")

            # 检查响应式设计相关CSS
            responsive_css = driver.find_elements(By.XPATH, "//link[contains(@href, 'responsive') or contains(@href, 'mobile')]")
            result["responsive_css_count"] = len(responsive_css)
            if responsive_css:
                print(f"✓ 发现响应式CSS文件: {len(responsive_css)}个")
            else:
                print("? 未明确发现响应式CSS文件")

        except:
            result["has_viewport"] = False
            result["responsive_css_count"] = 0
            print("移动端优化检查: 获取失败")
        
        print("--- SEO信息获取完成 ---")
        print("=" * 50)

        # 如果有多个标签页，关闭当前标签页
        if len(driver.window_handles) > 1:
            driver.close()
            driver.switch_to.window(driver.window_handles[0])

        # 简短等待再处理下一个URL（现在不需要长时间等待了）
        time.sleep(0.5)

    except Exception as e:
        print(f"处理URL时出错: {e}")
        print("=" * 50)
        # 即使出错也要添加基本结果，避免数据不一致
        if "url" not in result:
            result = {"url": url}
        # 为所有字段设置空值
        fields = [
            "title", "meta_description", "meta_keywords", "meta_robots", "viewport",
            "charset", "canonical", "meta_author", "og_title", "og_description",
            "og_image", "og_url", "og_type", "og_site_name", "twitter_card",
            "twitter_title", "twitter_description", "twitter_image", "twitter_site",
            "twitter_creator", "h1_count", "h2_count", "h3_count", "h4_count",
            "h5_count", "h6_count", "h1_text", "h2_text", "h3_text", "json_ld_count",
            "json_ld_content", "internal_links_count", "external_links_count",
            "total_links_count", "images_count", "images_without_alt", "images_seo_rate",
            "page_lang", "page_size", "text_length", "has_viewport", "responsive_css_count"
        ]
        for field in fields:
            if field not in result:
                result[field] = ""

    # 将结果添加到列表中
    results.append(result)
    print(f"第 {index + 1} 个URL处理完成")

# 所有URL处理完后关闭浏览器
driver.quit()
print("所有URL处理完成，Firefox浏览器已关闭")

# 将结果转换为DataFrame并保存为Excel
result_df = pd.DataFrame(results)
result_df.to_excel("url_metadata_result.xlsx", index=False)
print(f"结果已保存到 url_metadata_result.xlsx，共处理 {len(results)} 个URL")